#!/bin/bash

# erc-file-processing-bmi.sh
# Script for parallel file processing and tar creation

# Initialize variables
DATE=""
HOUR=""
QUARTER_HOUR=""
OSS=""
VENDOR=""
TECH=""
SOURCE_PATH=""
TARGET_PATH=""
PATTERN=""

# Function to display usage
usage() {
    echo "Usage: $0 -d date -h hour -q quarter_hour -o oss -s source_path -t target_path -p pattern [-v vendor] [-T tech]"
    echo ""
    echo "Mandatory options:"
    echo "  -d date         Date in format YYYYMMDD (e.g., 20250617)"
    echo "  -h hour         Hour range 00-23"
    echo "  -q quarter_hour Quarter hour [00, 15, 30, 45]"
    echo "  -o oss          OSS [ENM, ENM1, ENM2, ENM3, ENM4, ENM5]"
    echo "  -s source_path  Source directory path"
    echo "  -t target_path  Target directory path"
    echo "  -p pattern      File name pattern"
    echo ""
    echo "Optional options:"
    echo "  -v vendor       Vendor name"
    echo "  -T tech         Technology"
    echo ""
    exit 1
}

# Parse command line arguments
while getopts "d:h:q:o:v:T:s:t:p:" opt; do
    case $opt in
        d) DATE="$OPTARG" ;;
        h) HOUR="$OPTARG" ;;
        q) QUARTER_HOUR="$OPTARG" ;;
        o) OSS="$OPTARG" ;;
        v) VENDOR="$OPTARG" ;;
        T) TECH="$OPTARG" ;;
        s) SOURCE_PATH="$OPTARG" ;;
        t) TARGET_PATH="$OPTARG" ;;
        p) PATTERN="$OPTARG" ;;
        *) usage ;;
    esac
done

# Check mandatory parameters
if [[ -z "$DATE" || -z "$HOUR" || -z "$QUARTER_HOUR" || -z "$OSS" || -z "$SOURCE_PATH" || -z "$TARGET_PATH" || -z "$PATTERN" ]]; then
    echo "Error: Missing mandatory parameters"
    usage
fi

# Display parameters
echo "=== ERC File Processing BMI ==="
echo "Date: $DATE"
echo "Hour: $HOUR"
echo "Quarter Hour: $QUARTER_HOUR"
echo "OSS: $OSS"
echo "Source Path: $SOURCE_PATH"
echo "Target Path: $TARGET_PATH"
echo "Pattern: $PATTERN"
[[ -n "$VENDOR" ]] && echo "Vendor: $VENDOR"
[[ -n "$TECH" ]] && echo "Tech: $TECH"
echo "================================"

# Start timing
START_TIME=$(date +%s)
echo "Start time: $(date)"

# Step 1: Create list of files in parallel
echo "Step 1: Creating file list in parallel..."

# Change to source directory
cd "$SOURCE_PATH" || { echo "Error: Cannot change to source path $SOURCE_PATH"; exit 1; }

# Create temporary file list
FILE_LIST="/tmp/file_list_$$"
> "$FILE_LIST"

# Process OSS directories in parallel
for oss_dir in $(ls -d OSS/* 2>/dev/null); do
    (
        if [[ -d "$oss_dir" ]]; then
            # Generate file path based on pattern
            file_path="$oss_dir/$DATE$HOUR$QUARTER_HOUR.$OSS.$PATTERN"
            echo "$file_path" >> "$FILE_LIST"
            echo "Generated: $file_path"
        fi
    ) &
done

# Wait for all parallel processes to complete
wait

# Count input files
INPUT_FILE_COUNT=$(wc -l < "$FILE_LIST")
echo "Input file count: $INPUT_FILE_COUNT"

# Step 2: Create tar archive
echo "Step 2: Creating tar archive..."

# Generate tar filename
TAR_FILENAME="erc_${OSS}_${DATE}_${HOUR}${QUARTER_HOUR}"
[[ -n "$VENDOR" ]] && TAR_FILENAME="${TAR_FILENAME}_${VENDOR}"
[[ -n "$TECH" ]] && TAR_FILENAME="${TAR_FILENAME}_${TECH}"
TAR_FILENAME="${TAR_FILENAME}.tar"

TAR_PATH="$TARGET_PATH/$TAR_FILENAME"

# Create tar archive from file list
echo "Creating tar: $TAR_PATH"
tar -cf "$TAR_PATH" -T "$FILE_LIST" 2>/dev/null

# Count files in tar
TAR_FILE_COUNT=$(tar -tf "$TAR_PATH" 2>/dev/null | wc -l)
echo "Tar file count: $TAR_FILE_COUNT"

# Calculate completion time
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# Display summary
echo "================================"
echo "=== Processing Summary ==="
echo "Input files processed: $INPUT_FILE_COUNT"
echo "Files in tar archive: $TAR_FILE_COUNT"
echo "Tar file created: $TAR_PATH"
echo "Processing time: ${DURATION} seconds"
echo "End time: $(date)"
echo "================================"

# Cleanup
rm -f "$FILE_LIST"

echo "Processing completed successfully!"
