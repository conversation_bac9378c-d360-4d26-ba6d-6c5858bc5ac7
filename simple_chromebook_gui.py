import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk
import webbrowser
import re
import urllib.request
import urllib.error

class ChromebookApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Chromebook Web Browser")
        self.root.geometry("800x600")

        # Create main frame
        self.main_frame = tk.Frame(root, padx=20, pady=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Add a welcome label
        self.label = tk.Label(self.main_frame, text="Enter a URL to browse:", font=("Arial", 14))
        self.label.pack(pady=10)

        # Add URL entry frame
        self.url_frame = tk.Frame(self.main_frame)
        self.url_frame.pack(fill=tk.X, pady=10)

        # Add an entry field for URL
        self.entry = tk.Entry(self.url_frame, width=50, font=("Arial", 12))
        self.entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.entry.bind('<Return>', lambda event: self.submit_action())

        # Add buttons
        self.submit_button = tk.Button(self.url_frame, text="Go", command=self.submit_action, bg="#4CAF50", fg="white")
        self.submit_button.pack(side=tk.LEFT, padx=5)

        self.clear_button = tk.Button(self.url_frame, text="Clear", command=self.clear_action)
        self.clear_button.pack(side=tk.LEFT, padx=5)

        # Add dropdown menu for additional options
        self.options_var = tk.StringVar()
        self.options_dropdown = ttk.Combobox(self.url_frame, textvariable=self.options_var,
                                           values=["URL Viewer", "Page Info", "History", "Bookmarks", "Settings"],
                                           state="readonly", width=12)
        self.options_dropdown.pack(side=tk.LEFT, padx=5)
        self.options_dropdown.bind('<<ComboboxSelected>>', self.on_option_selected)

        # Add status/info area
        self.info_frame = tk.Frame(self.main_frame)
        self.info_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.info_label = tk.Label(self.info_frame, text="Browser Status:", font=("Arial", 12, "bold"))
        self.info_label.pack(anchor=tk.W)

        # Add scrolled text area for displaying information
        self.info_text = scrolledtext.ScrolledText(self.info_frame, height=15, wrap=tk.WORD, font=("Arial", 10))
        self.info_text.pack(fill=tk.BOTH, expand=True)

        # Add initial message
        self.info_text.insert(tk.END, "Welcome to Chromebook Web Browser!\n\n")
        self.info_text.insert(tk.END, "Instructions:\n")
        self.info_text.insert(tk.END, "1. Enter a URL in the text field above\n")
        self.info_text.insert(tk.END, "2. Click 'Go' or press Enter to open the website\n")
        self.info_text.insert(tk.END, "3. The website will open in your default browser\n\n")
        self.info_text.insert(tk.END, "Examples:\n")
        self.info_text.insert(tk.END, "- google.com\n")
        self.info_text.insert(tk.END, "- https://www.wikipedia.org\n")
        self.info_text.insert(tk.END, "- youtube.com\n\n")
        self.info_text.config(state=tk.DISABLED)

        # Initialize data storage
        self.browsing_history = []
        self.bookmarks = ["https://www.google.com", "https://www.wikipedia.org", "https://www.github.com"]
        
    def submit_action(self):
        url = self.entry.get().strip()
        if url:
            # Format URL if needed
            formatted_url = self.format_url(url)

            # Update info text
            self.info_text.config(state=tk.NORMAL)
            self.info_text.insert(tk.END, f"\n🌐 Opening: {formatted_url}\n")
            self.info_text.insert(tk.END, f"Time: {self.get_current_time()}\n")

            try:
                # Open URL in default browser
                webbrowser.open(formatted_url)
                self.info_text.insert(tk.END, "✅ Successfully opened in browser\n")

                # Add to browsing history
                self.browsing_history.append({
                    'url': formatted_url,
                    'time': self.get_current_time()
                })
            except Exception as e:
                self.info_text.insert(tk.END, f"❌ Error opening URL: {str(e)}\n")

            self.info_text.insert(tk.END, "-" * 50 + "\n")
            self.info_text.see(tk.END)
            self.info_text.config(state=tk.DISABLED)
        else:
            messagebox.showwarning("Warning", "Please enter a URL")

    def clear_action(self):
        self.entry.delete(0, tk.END)

    def format_url(self, url):
        """Format URL to ensure it has proper protocol"""
        url = url.strip()
        if not url:
            return url

        # If URL doesn't start with http:// or https://, add https://
        if not re.match(r'^https?://', url, re.IGNORECASE):
            url = 'https://' + url

        return url

    def get_current_time(self):
        """Get current time as string"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def on_option_selected(self, event):
        """Handle dropdown menu selection"""
        selected = self.options_var.get()

        if selected == "URL Viewer":
            self.show_url_viewer()
        elif selected == "Page Info":
            self.show_page_info()
        elif selected == "History":
            self.show_history()
        elif selected == "Bookmarks":
            self.show_bookmarks()
        elif selected == "Settings":
            self.show_settings()

        # Reset dropdown to empty
        self.options_dropdown.set("")

    def show_url_viewer(self):
        """Show URL content viewer"""
        url = self.entry.get().strip()
        if not url:
            messagebox.showwarning("Warning", "Please enter a URL first")
            return

        formatted_url = self.format_url(url)

        # Create new window for URL viewer
        viewer_window = tk.Toplevel(self.root)
        viewer_window.title(f"URL Viewer - {formatted_url}")
        viewer_window.geometry("900x700")

        # Add frame and scrolled text
        frame = tk.Frame(viewer_window, padx=10, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # URL info label
        info_label = tk.Label(frame, text=f"Viewing: {formatted_url}", font=("Arial", 12, "bold"))
        info_label.pack(pady=5)

        # Text area for content
        text_area = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Courier", 10))
        text_area.pack(fill=tk.BOTH, expand=True)

        # Fetch and display content
        try:
            text_area.insert(tk.END, "🔄 Fetching content...\n\n")
            viewer_window.update()

            # Fetch URL content
            req = urllib.request.Request(formatted_url, headers={'User-Agent': 'Mozilla/5.0'})
            with urllib.request.urlopen(req, timeout=10) as response:
                content = response.read().decode('utf-8', errors='ignore')

            text_area.delete(1.0, tk.END)
            text_area.insert(tk.END, f"✅ Successfully fetched content from: {formatted_url}\n")
            text_area.insert(tk.END, f"Content-Type: {response.headers.get('content-type', 'Unknown')}\n")
            text_area.insert(tk.END, f"Content-Length: {len(content)} characters\n")
            text_area.insert(tk.END, "=" * 80 + "\n\n")
            text_area.insert(tk.END, content)

        except urllib.error.URLError as e:
            text_area.delete(1.0, tk.END)
            text_area.insert(tk.END, f"❌ Error fetching URL: {str(e)}\n")
            text_area.insert(tk.END, "This could be due to:\n")
            text_area.insert(tk.END, "- Invalid URL\n")
            text_area.insert(tk.END, "- Network connectivity issues\n")
            text_area.insert(tk.END, "- Website blocking automated requests\n")
        except Exception as e:
            text_area.delete(1.0, tk.END)
            text_area.insert(tk.END, f"❌ Unexpected error: {str(e)}\n")

        text_area.config(state=tk.DISABLED)

    def show_page_info(self):
        """Show page information"""
        url = self.entry.get().strip()
        if not url:
            messagebox.showwarning("Warning", "Please enter a URL first")
            return

        formatted_url = self.format_url(url)

        try:
            req = urllib.request.Request(formatted_url, headers={'User-Agent': 'Mozilla/5.0'})
            with urllib.request.urlopen(req, timeout=10) as response:
                headers = dict(response.headers)
                status = response.status

            info_text = f"URL: {formatted_url}\n"
            info_text += f"Status Code: {status}\n"
            info_text += f"Content-Type: {headers.get('content-type', 'Unknown')}\n"
            info_text += f"Content-Length: {headers.get('content-length', 'Unknown')}\n"
            info_text += f"Server: {headers.get('server', 'Unknown')}\n"
            info_text += f"Last-Modified: {headers.get('last-modified', 'Unknown')}\n"

            messagebox.showinfo("Page Information", info_text)
        except Exception as e:
            messagebox.showerror("Error", f"Could not fetch page info: {str(e)}")

    def show_history(self):
        """Show browsing history"""
        if not self.browsing_history:
            messagebox.showinfo("History", "No browsing history available.")
            return

        history_window = tk.Toplevel(self.root)
        history_window.title("Browsing History")
        history_window.geometry("600x400")

        frame = tk.Frame(history_window, padx=10, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(frame, text="Browsing History", font=("Arial", 14, "bold")).pack(pady=5)

        text_area = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Arial", 10))
        text_area.pack(fill=tk.BOTH, expand=True)

        for i, entry in enumerate(reversed(self.browsing_history), 1):
            text_area.insert(tk.END, f"{i}. {entry['url']}\n")
            text_area.insert(tk.END, f"   Time: {entry['time']}\n\n")

        text_area.config(state=tk.DISABLED)

    def show_bookmarks(self):
        """Show bookmarks"""
        bookmarks_window = tk.Toplevel(self.root)
        bookmarks_window.title("Bookmarks")
        bookmarks_window.geometry("500x300")

        frame = tk.Frame(bookmarks_window, padx=10, pady=10)
        frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(frame, text="Bookmarks", font=("Arial", 14, "bold")).pack(pady=5)

        text_area = scrolledtext.ScrolledText(frame, wrap=tk.WORD, font=("Arial", 10))
        text_area.pack(fill=tk.BOTH, expand=True)

        for i, bookmark in enumerate(self.bookmarks, 1):
            text_area.insert(tk.END, f"{i}. {bookmark}\n")

        text_area.config(state=tk.DISABLED)

    def show_settings(self):
        """Show settings dialog"""
        settings_info = """Chromebook Web Browser Settings

Current Configuration:
• Default Protocol: HTTPS
• Timeout: 10 seconds
• User Agent: Mozilla/5.0
• History Tracking: Enabled
• Bookmarks: Enabled

Features:
• URL Viewer - View raw HTML content
• Page Info - Get HTTP headers and metadata
• History - Track visited URLs
• Bookmarks - Quick access to favorite sites

Version: 1.0
"""
        messagebox.showinfo("Settings", settings_info)

if __name__ == "__main__":
    root = tk.Tk()
    app = ChromebookApp(root)
    root.mainloop()